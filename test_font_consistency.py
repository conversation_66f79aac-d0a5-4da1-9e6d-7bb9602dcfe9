#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار توحيد الخطوط - التأكد من استخدام نفس الخط في جميع الدوال
"""

import json
from label_design import create_compact_label_image

def test_font_consistency():
    """اختبار توحيد الخطوط"""
    
    print("🔤 اختبار توحيد الخطوط")
    print("=" * 40)
    
    # تحميل الإعدادات
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("❌ ملف الإعدادات غير موجود")
        return
    
    print("📝 الخطوط في الإعدادات:")
    print(f"   • الخط الغامق: {config.get('font_bold_name')}")
    print(f"   • الخط العادي: {config.get('font_regular_name')}")
    print()
    
    # بيانات تجريبية مع نصوص عربية تحتوي على الألف
    test_data = {
        "labOrder": {
            "patient": {
                "name": "أحمد محمد الألفي الأسود"  # نص يحتوي على عدة أحرف ألف
            },
            "created_at_raw": "2024-01-15T10:30:00Z"
        },
        "sampleGroups": {
            "group1": {
                "sample_code": "LAB-2024-001234",
                "name": "Serum",
                "tests": [
                    {"name": "CBC"},
                    {"name": "الألف الأول"},     # تحليل عربي
                    {"name": "الباء الثاني"},    # تحليل عربي
                    {"name": "Creatinine"},
                    {"name": "الألف الأخير"},    # تحليل عربي آخر
                    {"name": "AST"}
                ]
            }
        }
    }
    
    print("📋 البيانات التجريبية:")
    print(f"   المريض: {test_data['labOrder']['patient']['name']}")
    print(f"   رمز العينة: {test_data['sampleGroups']['group1']['sample_code']}")
    print()
    
    print("🎨 إنشاء صورة مع الخط الموحد...")
    try:
        lab_order_info = test_data['labOrder']
        sample_group = test_data['sampleGroups']['group1']
        
        # إنشاء صورة بالخط الموحد
        label_image = create_compact_label_image(
            lab_order_info=lab_order_info,
            sample_group=sample_group,
            config=config
        )
        
        # حفظ الصورة
        output_file = "font_consistency_test.png"
        label_image.save(output_file, "PNG")
        
        print(f"✅ تم حفظ الصورة في: {output_file}")
        print(f"📏 أبعاد الصورة: {label_image.width} × {label_image.height} بكسل")
        
        # معلومات الخط المستخدم
        print(f"\n📝 معلومات الخط المستخدم:")
        print(f"   • الخط الغامق: {config.get('font_bold_name')}")
        print(f"   • الخط العادي: {config.get('font_regular_name')}")
        print(f"   • حجم خط المريض: {config.get('font_size_pt_patient')}pt")
        print(f"   • حجم خط التحاليل: {config.get('font_size_pt_tests')}pt")
        
        # نص التحاليل النهائي
        test_names = [test.get('name', '') for test in sample_group.get('tests', [])]
        test_names_str = '   '.join(test_names)
        print(f"\n📋 نص التحاليل:")
        print(f"   {test_names_str}")
        
        # تحليل النصوص العربية
        arabic_texts = []
        for test in sample_group.get('tests', []):
            name = test.get('name', '')
            if any('\u0600' <= char <= '\u06FF' for char in name):
                arabic_texts.append(name)
        
        if arabic_texts:
            print(f"\n🔤 النصوص العربية المكتشفة:")
            for text in arabic_texts:
                alif_count = text.count('أ') + text.count('ا') + text.count('إ') + text.count('آ')
                print(f"   • '{text}' - يحتوي على {alif_count} حرف ألف")
        
        print(f"\n✅ تم إنشاء الصورة بنجاح باستخدام خط {config.get('font_bold_name')}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الصورة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_font_consistency()
