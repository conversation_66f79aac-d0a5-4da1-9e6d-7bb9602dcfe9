#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار طباعة مبسط - لاختبار الطباعة بدون الخادم
"""

import queue
import json
from printer_core import load_config, process_print_request

def test_simple_print():
    """اختبار طباعة مبسط مع بيانات تجريبية"""
    
    print("🧪 اختبار الطباعة المبسط")
    print("=" * 40)
    
    # إنشاء طابور للسجل
    log_queue = queue.Queue()
    
    # تحميل الإعدادات
    config = load_config()
    print(f"📄 الطابعة المحددة: {config.get('printer_name', 'غير محدد')}")
    
    # بيانات تجريبية
    test_data = {
        "labOrder": {
            "patient": {
                "name": "أحمد محمد الألفي"  # اسم يحتوي على حرف الألف
            },
            "created_at_raw": "2024-01-15T10:30:00Z"
        },
        "sampleGroups": {
            "group1": {
                "sample_code": "TEST-001",
                "name": "Serum",
                "tests": [
                    {"name": "CBC"},
                    {"name": "Glucose"},
                    {"name": "الألف والباء"},  # تحليل عربي يحتوي على الألف
                    {"name": "Creatinine"},
                    {"name": "تحليل الدم"},    # تحليل عربي آخر
                    {"name": "AST"},
                    {"name": "الكوليسترول"}   # تحليل عربي يحتوي على حروف مختلفة
                ]
            }
        }
    }
    
    print("📋 البيانات التجريبية:")
    print(f"   المريض: {test_data['labOrder']['patient']['name']}")
    print(f"   رمز العينة: {test_data['sampleGroups']['group1']['sample_code']}")
    print(f"   نوع العينة: {test_data['sampleGroups']['group1']['name']}")
    print()
    
    # تشغيل الطباعة
    print("🖨️  بدء الطباعة...")
    try:
        success = process_print_request(test_data, config, log_queue)
        
        # عرض رسائل السجل
        print("\n📝 رسائل السجل:")
        while not log_queue.empty():
            message = log_queue.get()
            print(f"   {message}")
        
        if success:
            print("\n✅ نجحت الطباعة!")
        else:
            print("\n❌ فشلت الطباعة!")
            
    except Exception as e:
        print(f"\n❌ خطأ في الطباعة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_print()
