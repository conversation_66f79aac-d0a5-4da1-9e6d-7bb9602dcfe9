# دليل حل مشاكل الطباعة - LaraLab Print Agent

## 🔍 تشخيص المشاكل

### 1. تشغيل أداة التشخيص
```bash
python test_printer_diagnosis.py
```

هذه الأداة ستعرض:
- جميع الطابعات المتاحة في النظام
- حالة كل طابعة
- الإعدادات الحالية
- حالة الخطوط المطلوبة
- اقتراحات للحلول

### 2. اختبار الطباعة المبسط
```bash
python simple_print_test.py
```

هذا الاختبار سيقوم بـ:
- تحميل الإعدادات الحالية
- إنشاء ملصق تجريبي
- محاولة طباعته
- عرض رسائل السجل التفصيلية

## ❌ المشاكل الشائعة والحلول

### المشكلة 1: "الطابعة غير متاحة أو مطفأة"
**الأسباب المحتملة:**
- الطابعة غير متصلة أو مطفأة
- اسم الطابعة في الإعدادات خاطئ
- تعريفات الطابعة غير مثبتة

**الحلول:**
1. تأكد من أن الطابعة متصلة ومشغلة
2. شغل `test_printer_diagnosis.py` لرؤية الطابعات المتاحة
3. في البرنامج الرئيسي، اذهب إلى الإعدادات وحدد الطابعة الصحيحة
4. جرب طباعة صفحة اختبار من Windows للتأكد من عمل الطابعة

### المشكلة 2: "خطأ في البيانات المستلمة"
**الأسباب المحتملة:**
- البيانات المرسلة من الخادم غير مكتملة
- تنسيق البيانات خاطئ

**الحلول:**
1. تحقق من أن البيانات تحتوي على `labOrder` و `sampleGroups`
2. تأكد من أن `sampleGroups` هو كائن (object) وليس مصفوفة (array)

### المشكلة 3: "خطأ في تحميل الخط"
**الأسباب المحتملة:**
- ملفات الخطوط مفقودة من مجلد `fonts`
- مسار الخطوط خاطئ

**الحلول:**
1. تأكد من وجود مجلد `fonts` في نفس مجلد البرنامج
2. تأكد من وجود الملفات التالية:
   - `Cairo-Bold.ttf`
   - `Cairo-Regular.ttf`
   - `NotoNaskhArabic-Bold.ttf`
   - `NotoNaskhArabic-Regular.ttf`

### المشكلة 4: "فشل في إنشاء صورة الملصق"
**الأسباب المحتملة:**
- خطأ في إعدادات الأبعاد
- مشكلة في الخطوط
- بيانات ناقصة

**الحلول:**
1. تحقق من إعدادات `label_width_mm` و `label_height_mm` في `config.json`
2. تأكد من أن قيمة `dpi` صحيحة (عادة 300)
3. شغل `simple_print_test.py` لاختبار إنشاء الملصق

## ⚙️ إعدادات مهمة في config.json

```json
{
    "printer_name": "اسم_الطابعة_الصحيح",
    "label_width_mm": 50,
    "label_height_mm": 25,
    "dpi": 300,
    "listening_port": 9898
}
```

## 📝 رسائل السجل المهمة

- `✅ تمت الطباعة بنجاح`: الطباعة نجحت
- `❌ الطابعة غير متاحة`: مشكلة في الطابعة
- `❌ خطأ في البيانات`: مشكلة في البيانات المرسلة
- `⚠️ Font loading error`: مشكلة في تحميل الخطوط

## 🔧 خطوات التشخيص المتقدم

1. **تشغيل أداة التشخيص:**
   ```bash
   python test_printer_diagnosis.py
   ```

2. **اختبار الطباعة:**
   ```bash
   python simple_print_test.py
   ```

3. **فحص السجل في البرنامج الرئيسي:**
   - شغل `gui.py`
   - راقب منطقة السجل في الأسفل
   - جرب إرسال طلب طباعة
   - اقرأ رسائل الخطأ التفصيلية

4. **اختبار الطابعة من Windows:**
   - اذهب إلى Control Panel > Devices and Printers
   - انقر بالزر الأيمن على الطابعة
   - اختر "Print test page"

## 📞 طلب المساعدة

إذا استمرت المشكلة، يرجى تقديم المعلومات التالية:
1. نتائج `test_printer_diagnosis.py`
2. رسائل السجل من `simple_print_test.py`
3. محتوى ملف `config.json`
4. نوع وموديل الطابعة المستخدمة
