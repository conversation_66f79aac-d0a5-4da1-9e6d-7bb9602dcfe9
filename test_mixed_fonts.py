#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الخطوط المختلطة - عربي للأسماء، إنجليزي للتحاليل
"""

import json
from label_design import create_compact_label_image

def test_mixed_fonts():
    """اختبار الخطوط المختلطة"""
    
    print("🔤 اختبار الخطوط المختلطة")
    print("=" * 50)
    
    # تحميل الإعدادات
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("❌ ملف الإعدادات غير موجود")
        return
    
    print("📝 إعدادات الخطوط:")
    print(f"   • الخط العربي الغامق: {config.get('font_bold_name')}")
    print(f"   • الخط العربي العادي: {config.get('font_regular_name')}")
    print(f"   • الخط الإنجليزي الغامق: {config.get('font_english_bold')}")
    print(f"   • الخط الإنجليزي العادي: {config.get('font_english_regular')}")
    print()
    
    # بيانات تجريبية مع نصوص مختلطة
    test_data = {
        "labOrder": {
            "patient": {
                "name": "أحمد محمد الألفي الأسود"  # اسم عربي بخط عربي
            },
            "created_at_raw": "2024-01-15T10:30:00Z"
        },
        "sampleGroups": {
            "group1": {
                "sample_code": "LAB-2024-001234",  # رمز إنجليزي
                "name": "Serum",                   # نوع إنجليزي
                "tests": [
                    {"name": "CBC"},                    # تحليل إنجليزي
                    {"name": "Complete Blood Count"},   # تحليل إنجليزي طويل
                    {"name": "Glucose"},               # تحليل إنجليزي
                    {"name": "Creatinine"},            # تحليل إنجليزي
                    {"name": "Liver Function Test"},   # تحليل إنجليزي طويل
                    {"name": "Lipid Profile"},         # تحليل إنجليزي
                    {"name": "Thyroid Function"},      # تحليل إنجليزي
                    {"name": "Hemoglobin A1c"}         # تحليل إنجليزي
                ]
            }
        }
    }
    
    print("📋 البيانات التجريبية:")
    print(f"   المريض (عربي): {test_data['labOrder']['patient']['name']}")
    print(f"   رمز العينة (إنجليزي): {test_data['sampleGroups']['group1']['sample_code']}")
    print(f"   نوع العينة (إنجليزي): {test_data['sampleGroups']['group1']['name']}")
    print(f"   عدد التحاليل: {len(test_data['sampleGroups']['group1']['tests'])}")
    print()
    
    print("🎨 إنشاء صورة بالخطوط المختلطة...")
    try:
        lab_order_info = test_data['labOrder']
        sample_group = test_data['sampleGroups']['group1']
        
        # إنشاء صورة بالخطوط المختلطة
        label_image = create_compact_label_image(
            lab_order_info=lab_order_info,
            sample_group=sample_group,
            config=config
        )
        
        # حفظ الصورة
        output_file = "mixed_fonts_preview.png"
        label_image.save(output_file, "PNG")
        
        print(f"✅ تم حفظ الصورة في: {output_file}")
        print(f"📏 أبعاد الصورة: {label_image.width} × {label_image.height} بكسل")
        
        # تحليل النصوص
        print(f"\n📊 تحليل النصوص:")
        print(f"   • اسم المريض: عربي - خط {config.get('font_bold_name')}")
        print(f"   • رمز العينة: إنجليزي - خط {config.get('font_english_bold')}")
        print(f"   • التاريخ: إنجليزي - خط {config.get('font_english_regular')}")
        print(f"   • نوع العينة: إنجليزي - خط {config.get('font_english_bold')}")
        print(f"   • التحاليل: إنجليزي - خط {config.get('font_english_regular')}")
        
        # عرض التحاليل
        test_names = [test.get('name', '') for test in sample_group.get('tests', [])]
        test_names_str = '   '.join(test_names)
        print(f"\n📋 التحاليل (إنجليزي):")
        print(f"   {test_names_str}")
        
        print(f"\n✅ تم إنشاء الصورة بنجاح بالخطوط المختلطة!")
        print(f"   🔤 اسم المريض: خط عربي واضح")
        print(f"   🔤 التحاليل: خط إنجليزي واضح")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الصورة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_mixed_fonts()
