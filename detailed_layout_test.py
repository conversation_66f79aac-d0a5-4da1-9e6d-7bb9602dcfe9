#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تخطيط مفصل - لتحليل تخطيط الستكر وإعطاء معلومات مفصلة
"""

import json
from label_design import create_compact_label_image
from PIL import ImageDraw, ImageFont

def analyze_label_layout():
    """تحليل تخطيط الستكر وإعطاء معلومات مفصلة"""
    
    print("🔍 تحليل تخطيط الستكر المفصل")
    print("=" * 50)
    
    # تحميل الإعدادات
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("❌ ملف الإعدادات غير موجود")
        return
    
    # بيانات تجريبية
    test_data = {
        "labOrder": {
            "patient": {
                "name": "أحمد محمد علي الخالدي"
            },
            "created_at_raw": "2024-01-15T10:30:00Z"
        },
        "sampleGroups": {
            "group1": {
                "sample_code": "LAB-2024-001234",
                "name": "Serum",
                "tests": [
                    {"name": "CBC"},
                    {"name": "Glucose"},
                    {"name": "Urea"},
                    {"name": "Creatinine"},
                    {"name": "ALT"},
                    {"name": "AST"}
                ]
            }
        }
    }
    
    # حساب الأبعاد
    dpi = config.get("dpi", 300)
    label_width_mm = config.get("label_width_mm", 50)
    label_height_mm = config.get("label_height_mm", 25)
    
    def mm_to_px(mm): 
        return int(mm * dpi / 25.4)
    
    label_width_px = mm_to_px(label_width_mm)
    label_height_px = mm_to_px(label_height_mm)
    padding_px = mm_to_px(config.get("padding_mm", 0.5))
    
    print("📐 معلومات الأبعاد:")
    print(f"   • أبعاد الستكر: {label_width_mm}mm × {label_height_mm}mm")
    print(f"   • أبعاد بالبكسل: {label_width_px} × {label_height_px} px")
    print(f"   • الحشو: {config.get('padding_mm', 0.5)}mm ({padding_px}px)")
    print(f"   • المساحة المتاحة: {label_width_px - 2*padding_px} × {label_height_px - 2*padding_px} px")
    print()
    
    print("🎨 إعدادات الخطوط:")
    print(f"   • خط المريض: {config.get('font_size_pt_patient', 10)}pt")
    print(f"   • خط رمز العينة: {config.get('font_size_pt_id', 9)}pt")
    print(f"   • خط التاريخ: {config.get('font_size_pt_regular', 8)}pt")
    print(f"   • خط التحاليل: {config.get('font_size_pt_tests', 9)}pt")
    print()
    
    print("📊 إعدادات التباعد:")
    print(f"   • التباعد بين الأقسام: {config.get('section_gap_px', 6)}px")
    print(f"   • التباعد داخل التحاليل: {config.get('line_padding_px', 2)}px")
    print()
    
    print("📏 إعدادات الباركود:")
    barcode_height_mm = config.get('barcode_height_mm', 7.0)
    barcode_height_px = mm_to_px(barcode_height_mm)
    print(f"   • ارتفاع الباركود: {barcode_height_mm}mm ({barcode_height_px}px)")
    print(f"   • عامل تكبير العرض: {config.get('barcode_width_factor', 1.3)}")
    print()
    
    # إنشاء الستكر
    print("🎨 إنشاء الستكر...")
    try:
        lab_order_info = test_data['labOrder']
        sample_group = test_data['sampleGroups']['group1']
        
        label_image = create_compact_label_image(
            lab_order_info=lab_order_info,
            sample_group=sample_group,
            config=config
        )
        
        # حفظ الصورة مع معلومات مفصلة
        output_file = "detailed_label_preview.png"
        label_image.save(output_file, "PNG")
        
        print(f"✅ تم حفظ الستكر المفصل في: {output_file}")
        
        # تحليل استخدام المساحة
        total_area = label_width_px * label_height_px
        barcode_area = (label_width_px * 0.8) * barcode_height_px  # تقدير تقريبي
        barcode_percentage = (barcode_area / total_area) * 100
        
        print(f"\n📊 تحليل استخدام المساحة:")
        print(f"   • المساحة الإجمالية: {total_area:,} بكسل")
        print(f"   • مساحة الباركود التقديرية: {barcode_area:,.0f} بكسل ({barcode_percentage:.1f}%)")
        print(f"   • المساحة المتبقية للنصوص: {total_area - barcode_area:,.0f} بكسل ({100-barcode_percentage:.1f}%)")
        
        # معلومات التحاليل
        tests_count = len(sample_group.get('tests', []))
        tests_text = ' ◦ '.join([test.get('name', '') for test in sample_group.get('tests', [])])
        
        print(f"\n📋 معلومات التحاليل:")
        print(f"   • عدد التحاليل: {tests_count}")
        print(f"   • طول النص: {len(tests_text)} حرف")
        print(f"   • النص: {tests_text[:50]}{'...' if len(tests_text) > 50 else ''}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الستكر: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_label_layout()
