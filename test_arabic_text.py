#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار معالجة النصوص العربية
"""

import arabic_reshaper
from bidi.algorithm import get_display

def test_arabic_processing():
    """اختبار معالجة النصوص العربية"""
    
    print("🔤 اختبار معالجة النصوص العربية")
    print("=" * 40)
    
    # نصوص تجريبية
    test_texts = [
        "أحمد محمد علي الخالدي",
        "مريض تجريبي",
        "الألف والباء والتاء",
        "LAB-2024-001234",  # نص إنجليزي
        "CBC",
        "Glucose",
        "تحليل الدم الشامل"
    ]
    
    print("📝 النصوص الأصلية والمعالجة:")
    print("-" * 40)
    
    for text in test_texts:
        print(f"الأصلي: {text}")
        
        # تحقق من وجود نصوص عربية
        has_arabic = any('\u0600' <= char <= '\u06FF' for char in text)
        print(f"يحتوي على عربي: {has_arabic}")
        
        if has_arabic:
            try:
                # معالجة النص العربي
                reshaped = arabic_reshaper.reshape(text)
                processed = get_display(reshaped)
                print(f"المعالج: {processed}")
                
                # تحقق من الحروف المشكلة
                if 'أ' in text or 'ا' in text:
                    print("⚠️  يحتوي على حرف الألف")
                    
            except Exception as e:
                print(f"❌ خطأ في المعالجة: {e}")
                processed = text
        else:
            processed = text
            print(f"المعالج: {processed}")
        
        print("-" * 20)
    
    print("\n🔍 اختبار حروف مشكلة:")
    problematic_chars = ['أ', 'ا', 'إ', 'آ', 'ة', 'ى', 'ي']
    
    for char in problematic_chars:
        try:
            reshaped = arabic_reshaper.reshape(char)
            processed = get_display(reshaped)
            print(f"'{char}' -> '{processed}'")
        except Exception as e:
            print(f"'{char}' -> خطأ: {e}")

if __name__ == "__main__":
    test_arabic_processing()
