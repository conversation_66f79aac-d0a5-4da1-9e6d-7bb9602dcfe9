import os
import sys
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display
from datetime import datetime, timezone, timedelta
from io import BytesIO
import barcode
from barcode.writer import ImageWriter

def get_font_path(font_name):
    """Get the correct path to a font file, whether running as script or exe"""
    import sys
    
    # Check if running as bundled executable
    if getattr(sys, 'frozen', False):
        base_path = sys._MEIPASS
        # print(f"Running as executable. Base path: {base_path}")
    else:
        base_path = os.path.abspath(".")
        # print(f"Running as script. Base path: {base_path}")
    
    font_path = os.path.join(base_path, "fonts", font_name)
    # print(f"Trying font path: {font_path}")
    # print(f"Path exists: {os.path.exists(font_path)}")
    
    if os.path.exists(font_path):
        return font_path
    
    # List all files in the fonts directory
    # print(f"Contents of {fonts_dir}:")
    # for file in os.listdir(fonts_dir):
    #     print(f"  - {file}")
    
    # Try alternative paths
    alt_paths = [
        os.path.join("fonts", font_name),
        os.path.join(os.path.dirname(__file__), "fonts", font_name),
        font_name
    ]
    
    for path in alt_paths:
        # print(f"Trying alternative path: {path}")
        # print(f"Path exists: {os.path.exists(path)}")
        if os.path.exists(path):
            return path
    
    return None  # Font not found

# Configure Arabic reshaper explicitly
arabic_reshaper_config = {
    'delete_harakat': False,
    'support_ligatures': True,
    'use_unshaped_instead_of_isolated': False,
    'shift_harakat_position': False
}
reshaper = arabic_reshaper.ArabicReshaper(configuration=arabic_reshaper_config)

def ar_text(txt):
    # Use our configured reshaper instance
    reshaped_text = reshaper.reshape(txt)
    return get_display(reshaped_text)

def create_label(lab_order_info, sample_group, config):
    """
    Creates a compact label for medical samples with fixed dimensions of 50x25mm.
    """
    # --- Fixed Dimensions ---
    LABEL_WIDTH_MM = 50.0
    LABEL_HEIGHT_MM = 25.0
    DPI = 300
    PADDING_MM = 0.5
    BARCODE_HEIGHT_MM = 4.0

    # Font configuration
    font_name = config.get("font_bold_name", "Cairo-Bold.ttf")
    font_regular_name = config.get("font_regular_name", "Cairo-Regular.ttf")
    
    # Convert measurements
    def mm_to_px(mm): return int(mm * DPI / 25.4)
    def pt_to_px(pt): return int(pt * DPI / 72)
    
    # Convert dimensions to pixels
    label_width_px = mm_to_px(LABEL_WIDTH_MM)
    label_height_px = mm_to_px(LABEL_HEIGHT_MM)
    padding_px = mm_to_px(PADDING_MM)
    
    # Font setup - fixed sizes
    try:
        font_path_bold = get_font_path(font_name)
        font_path_regular = get_font_path(font_regular_name)
        font_patient = ImageFont.truetype(font_path_bold, pt_to_px(6))  # Patient name: 6pt
        font_bold = ImageFont.truetype(font_path_bold, pt_to_px(5))     # Bold text: 5pt
        font_regular = ImageFont.truetype(font_path_regular, pt_to_px(5))  # Regular: 5pt
    except Exception as e:
        print(f"⚠️ Font loading error: {e}")
        font_patient = font_bold = font_regular = ImageFont.load_default()

    # Create image
    image = Image.new("RGB", (label_width_px, label_height_px), "white")
    draw = ImageDraw.Draw(image)
    
    def process_text(text):
        """Handle Arabic text processing"""
        if any('\u0600' <= char <= '\u06FF' for char in str(text)):
            return get_display(reshaper.reshape(str(text)))
        return str(text)

    def draw_dashed_line(y):
        """Draw a dashed line at given Y position"""
        dash_len, gap_len = 3, 2
        x = padding_px
        while x < label_width_px - padding_px:
            end = min(x + dash_len, label_width_px - padding_px)
            draw.line([(x, y), (end, y)], fill="#000000", width=1)
            x += dash_len + gap_len
        return y + 2  # Return next Y position

    # Extract data
    patient_name = lab_order_info.get('patient', {}).get('name', 'N/A')
    sample_code = sample_group.get('sample_code', 'N/A')
    sample_type = sample_group.get('name', 'N/A')
    test_names = [test.get('name', '') for test in sample_group.get('tests', [])]
    test_names_str = ', '.join(test_names)
    
    try:
        dt_utc = datetime.fromisoformat(lab_order_info.get('created_at_raw', '').replace("Z", "+00:00"))
        date_str = dt_utc.astimezone(timezone(timedelta(hours=3))).strftime("%d/%m/%y")
    except (ValueError, TypeError):
        date_str = "N/A"

    current_y = padding_px

    # 1. Barcode Section
    barcode_writer = ImageWriter(format="PNG")
    ean = barcode.get('code128', sample_code, writer=barcode_writer)
    barcode_options = {
        'module_height': BARCODE_HEIGHT_MM,
        'write_text': False,
        'quiet_zone': 0.5
    }
    
    barcode_fp = BytesIO()
    ean.write(barcode_fp, options=barcode_options)
    barcode_fp.seek(0)
    barcode_img = Image.open(barcode_fp)
    
    # Resize barcode to fixed height
    barcode_height_px = mm_to_px(BARCODE_HEIGHT_MM)
    ratio = barcode_height_px / barcode_img.height
    new_width = int(barcode_img.width * ratio)
    barcode_img = barcode_img.resize((new_width, barcode_height_px), Image.Resampling.LANCZOS)
    
    # Center barcode
    barcode_x = (label_width_px - barcode_img.width) // 2
    image.paste(barcode_img, (barcode_x, current_y))
    current_y += barcode_height_px + 2

    # 2. Sample ID
    id_text = process_text(sample_code)
    id_width = draw.textlength(id_text, font=font_bold)
    id_x = (label_width_px - id_width) / 2
    draw.text((id_x, current_y), id_text, font=font_bold, fill="#000000")
    id_height = draw.textbbox((0, 0), id_text, font=font_bold)[3]
    current_y += id_height + 2

    # 3. Patient Info Section
    current_y = draw_dashed_line(current_y)  # Top line
    
    # Calculate widths for 3-part line
    date_width = draw.textlength(process_text(date_str), font=font_regular)
    type_width = draw.textlength(process_text(sample_type), font=font_bold)
    available_width = label_width_px - date_width - type_width - (4 * padding_px)
    
    # Truncate patient name if needed
    processed_name = process_text(patient_name)
    while draw.textlength(processed_name, font=font_patient) > available_width and len(processed_name) > 0:
        patient_name = patient_name[:-1]
        processed_name = process_text(patient_name + "...")
    
    # Draw the three elements
    draw.text((label_width_px - padding_px, current_y), process_text(sample_type), 
              font=font_bold, fill="#000000", anchor="ra")
    draw.text((padding_px, current_y), process_text(date_str), 
              font=font_regular, fill="#000000", anchor="la")
    
    name_width = draw.textlength(processed_name, font=font_patient)
    name_x = (label_width_px - name_width) / 2
    draw.text((name_x, current_y), processed_name, font=font_patient, fill="#000000")
    
    line_height = max(
        draw.textbbox((0, 0), "Aj", font=font_patient)[3],
        draw.textbbox((0, 0), "Aj", font=font_bold)[3],
        draw.textbbox((0, 0), "Aj", font=font_regular)[3]
    )
    current_y += line_height + 2
    
    current_y = draw_dashed_line(current_y)  # Bottom line

    # 4. Tests List
    if test_names:
        current_y += 2  # Space after dashed line
        available_width = label_width_px - (2 * padding_px)
        
        words = test_names_str.split(', ')
        line = ""
        
        for i, word in enumerate(words):
            separator = ", " if line else ""
            test_line = line + separator + word
            processed_line = process_text(test_line)
            
            if draw.textlength(processed_line, font=font_regular) <= available_width:
                line = test_line
            else:
                if line:  # Draw current line
                    draw.text((padding_px, current_y), process_text(line), 
                             font=font_regular, fill="#000000", anchor="la")
                    current_y += draw.textbbox((0, 0), "Aj", font=font_regular)[3] + 1
                line = word
        
        # Draw last line
        if line and current_y < label_height_px - padding_px:
            draw.text((padding_px, current_y), process_text(line), 
                     font=font_regular, fill="#000000", anchor="la")

    return image


def create_sample_label_image(
    lab_order_info, sample_group,
    label_width_px, label_height_px,
    font_family="Cairo-Bold.ttf",
    font_size_large=32,
    font_size_small=22,
    line_padding=5,
    bg_color="white", text_color="#000000"
):
    """
    Creates a single label for a sample group based on the new data structure.
    """
    # --- Font Setup ---
    font_path = get_font_path(font_family)
    if not font_path:
        print(f"⚠️ Font {font_family} not found, using default font.")
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    else:
        try:
            font_large = ImageFont.truetype(font_path, font_size_large)
            font_small = ImageFont.truetype(font_path, font_size_small)
        except Exception as e:
            print(f"⚠️ Error loading font {font_family}: {e}")
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()

    image = Image.new("RGB", (label_width_px, label_height_px), bg_color)
    draw = ImageDraw.Draw(image)

    # --- Data Extraction and Formatting ---
    patient_name = lab_order_info.get('patient', {}).get('name', 'N/A')
    sample_code = sample_group.get('sample_code', 'N/A')
    sample_type = sample_group.get('name', 'N/A')
    order_date_raw = lab_order_info.get('created_at_raw', '')
    
    # Format date
    try:
        dt_utc = datetime.fromisoformat(order_date_raw.replace("Z", "+00:00"))
        dt_local = dt_utc.astimezone(timezone(timedelta(hours=3))) # Baghdad Time
        date_str = dt_local.strftime("%Y-%m-%d %I:%M %p")
    except (ValueError, TypeError):
        date_str = order_date_raw

    # Format tests list, wrap it if it's too long
    tests = sample_group.get('tests', [])
    test_names = ', '.join([test.get('name', '') for test in tests])

    # --- Text Wrapping for Tests ---
    available_width = label_width_px - 20 # 10px padding on each side
    
    def get_wrapped_lines(text, font, max_width):
        lines = []
        if not text:
            return lines
        
        words = text.split(' ')
        current_line = ""
        for word in words:
            # Reshape and check width for each potential line
            reshaped_word = ar_text(word)
            if draw.textbbox((0,0), ar_text(current_line + " " + word), font=font)[2] <= max_width:
                current_line += " " + word
            else:
                lines.append(current_line.strip())
                current_line = word
        lines.append(current_line.strip())
        return lines

    wrapped_test_lines = get_wrapped_lines(test_names, font_small, available_width)
    
    # --- Text Elements ---
    # Using tuples of (text, font)
    lines_to_draw = [
        (patient_name, font_large),
        (sample_code, font_large),
        (sample_type, font_small),
        (date_str, font_small)
    ]
    # Add wrapped test lines
    for line in wrapped_test_lines:
        lines_to_draw.append((line, font_small))

    # --- Drawing ---
    y_cursor = 10  # Start with a top padding
    for text, font in lines_to_draw:
        if not text: continue
        
        # Reshape for Arabic display
        display_text = ar_text(text)
        
        # Get text dimensions
        bbox = draw.textbbox((0, 0), display_text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        # Center text horizontally
        x_pos = (label_width_px - text_width) / 2
        
        # Draw text and move cursor down
        draw.text((x_pos, y_cursor), display_text, fill=text_color, font=font)
        y_cursor += text_height + line_padding
        
    return image


def create_compact_label_image(
    lab_order_info, sample_group, config
):
    """
    Creates a compact, correctly formatted label for a sample group,
    matching the detailed design specification.
    """
    # --- 1. Configuration & Unit Conversion ---
    font_name = config.get("font_bold_name", "Cairo-Bold.ttf")
    font_regular_name = config.get("font_regular_name", "Cairo-Regular.ttf")
    label_width_mm = float(config.get("label_width_mm", 50))
    label_height_mm = float(config.get("label_height_mm", 25))
    dpi = int(config.get("dpi", 300))
    
    # تحسينات التصميم: رفع للأعلى، باركود أطول، تحاليل أوضح
    padding_mm = config.get("padding_mm", 0.5)  # تقليل المسافة العلوية لرفع التصميم
    line_padding_px = int(config.get("line_padding_px", 2))   # تقليل التباعد داخل قائمة التحاليل
    section_gap_px = int(config.get("section_gap_px", 6)) # تقليل التباعد بين الأقسام لرفع التصميم
    barcode_height_mm = float(config.get("barcode_height_mm", 7.0)) # ارتفاع الباركود
    barcode_width_factor = float(config.get("barcode_width_factor", 1.3)) # عامل تكبير عرض الباركود
    font_size_pt_patient = int(config.get("font_size_pt_patient", 10)) # تكبير خط المريض
    font_size_pt_id = int(config.get("font_size_pt_id", 9)) # تكبير خط رمز العينة
    font_size_pt_regular = int(config.get("font_size_pt_regular", 8)) # للتاريخ
    font_size_pt_tests = int(config.get("font_size_pt_tests", 9))     # تكبير خط التحاليل لوضوح أكبر
    
    text_color = config.get("text_color", "#000000")
    bg_color = config.get("bg_color", "white")

    def pt_to_px(pt):
        return int(pt * dpi / 72)

    def mm_to_px(mm):
        return int(mm * dpi / 25.4)

    label_width_px = mm_to_px(label_width_mm)
    label_height_px = mm_to_px(label_height_mm)
    padding_px = mm_to_px(padding_mm)
    barcode_height_px = mm_to_px(barcode_height_mm)
    content_width = label_width_px - (2 * padding_px)

    # --- 2. Font Setup ---
    try:
        font_path_bold = get_font_path(font_name)
        font_path_regular = get_font_path(font_regular_name)
        font_patient = ImageFont.truetype(font_path_bold, pt_to_px(font_size_pt_patient))
        font_id_type = ImageFont.truetype(font_path_bold, pt_to_px(font_size_pt_id))
        font_regular = ImageFont.truetype(font_path_regular, pt_to_px(font_size_pt_regular))
        font_tests = ImageFont.truetype(font_path_regular, pt_to_px(font_size_pt_tests)) # خط التحاليل
    except Exception as e:
        print(f"⚠️ Error loading font: {e}")
        font_patient = font_id_type = font_regular = font_tests = ImageFont.load_default()

    # --- 3. Image & Helper Setup ---
    image = Image.new("RGB", (label_width_px, label_height_px), bg_color)
    draw = ImageDraw.Draw(image)
    current_y = padding_px
    
    def process_text(text):
        """معالجة محسنة للنصوص العربية مع تحسين حرف الألف"""
        if not text:
            return ""

        text_str = str(text).strip()
        if not text_str:
            return ""

        # تحقق من وجود نصوص عربية
        if any('\u0600' <= char <= '\u06FF' for char in text_str):
            try:
                # تحسين خاص لحرف الألف والحروف المشكلة
                # استبدال الحروف المشكلة بأشكالها المحسنة
                text_str = text_str.replace('أ', 'أ')  # التأكد من شكل الألف الصحيح
                text_str = text_str.replace('إ', 'إ')  # الألف مع الهمزة تحت
                text_str = text_str.replace('آ', 'آ')  # الألف مع المدة

                # معالجة محسنة للنصوص العربية
                reshaped = reshaper.reshape(text_str)
                processed = get_display(reshaped)

                # تسجيل للتشخيص
                if 'أ' in text_str or 'ا' in text_str:
                    print(f"معالجة نص يحتوي على الألف: '{text_str}' -> '{processed}'")

                return processed
            except Exception as e:
                print(f"تحذير: مشكلة في معالجة النص العربي '{text_str}': {e}")
                return text_str
        return text_str

    def draw_dashed_line(y, x_start, x_end, dash_len=5, gap_len=3):
        for x in range(x_start, x_end, dash_len + gap_len):
            draw.line([(x, y), (x + dash_len, y)], fill=text_color, width=1)
        return y + line_padding_px + 1

    # --- 4. Data Extraction & Formatting ---
    patient_name = lab_order_info.get('patient', {}).get('name', 'N/A')
    sample_code = sample_group.get('sample_code', 'N/A')
    sample_type = sample_group.get('name', 'N/A')
    test_names_str = '   '.join([test.get('name', '') for test in sample_group.get('tests', [])])
    
    try:
        dt_utc = datetime.fromisoformat(lab_order_info.get('created_at_raw', '').replace("Z", "+00:00"))
        date_str = dt_utc.astimezone(timezone(timedelta(hours=3))).strftime("%d/%m/%Y")
    except (ValueError, TypeError):
        date_str = "N/A"

    # --- Section 1: Barcode (محسن - أطول وأوضح) ---
    barcode_writer = ImageWriter(format="PNG")
    ean = barcode.get('code128', sample_code, writer=barcode_writer)
    barcode_options = {
        'module_height': barcode_height_mm,
        'module_width': 0.33,  # تقليل عرض الوحدة لجعل الباركود أكثر كثافة
        'write_text': False,
        'quiet_zone': 0.3  # تقليل المنطقة الهادئة
    }

    barcode_fp = BytesIO()
    ean.write(barcode_fp, options=barcode_options)
    barcode_fp.seek(0)
    barcode_img = Image.open(barcode_fp)

    # تطبيق عامل التكبير للعرض مع الحفاظ على الارتفاع
    ratio = barcode_height_px / barcode_img.height
    new_barcode_width = int(barcode_img.width * ratio * barcode_width_factor)

    # التأكد من أن الباركود لا يتجاوز عرض المحتوى
    max_barcode_width = int(content_width * 0.95)  # 95% من عرض المحتوى
    if new_barcode_width > max_barcode_width:
        new_barcode_width = max_barcode_width

    barcode_img = barcode_img.resize((new_barcode_width, barcode_height_px), Image.Resampling.LANCZOS)

    barcode_x = (label_width_px - new_barcode_width) // 2
    image.paste(barcode_img, (barcode_x, int(current_y)))
    current_y += barcode_height_px + section_gap_px

    # --- Section 2: Sample Code & Date ---
    processed_id = process_text(sample_code)
    processed_date = process_text(date_str)
    
    draw.text((padding_px, current_y), processed_date, font=font_regular, fill=text_color, anchor="la")
    draw.text((label_width_px - padding_px, current_y), processed_id, font=font_id_type, fill=text_color, anchor="ra")
    
    id_bbox = draw.textbbox((0, 0), processed_id, font=font_id_type)
    date_bbox = draw.textbbox((0, 0), processed_date, font=font_regular)
    current_y += max(id_bbox[3] - id_bbox[1], date_bbox[3] - date_bbox[1]) + section_gap_px

    # --- Section 3: Patient Name & Sample Type ---
    processed_type = process_text(sample_type)
    
    def truncate_text_with_ellipsis(text, font, max_width):
        if draw.textlength(text, font) <= max_width:
            return text
        ellipsis = "..."
        while draw.textlength(text + ellipsis, font) > max_width and len(text) > 0:
            text = text[:-1]
        return text + ellipsis
    
    type_width = draw.textlength(processed_type, font=font_id_type)
    name_max_width = content_width - type_width - mm_to_px(2)
    
    truncated_name = truncate_text_with_ellipsis(patient_name, font_patient, name_max_width)
    processed_name = process_text(truncated_name)
    
    draw.text((padding_px, current_y), processed_type, font=font_id_type, fill=text_color, anchor="la")
    draw.text((label_width_px - padding_px, current_y), processed_name, font=font_patient, fill=text_color, anchor="ra")
    
    name_bbox = draw.textbbox((0, 0), processed_name, font=font_patient)
    type_bbox = draw.textbbox((0, 0), processed_type, font=font_id_type)
    current_y += max(name_bbox[3] - name_bbox[1], type_bbox[3] - type_bbox[1]) + section_gap_px

    # --- Section 4: Tests List ---
    current_y += section_gap_px / 2 # مسافة أقل قبل قائمة التحاليل

    def get_wrapped_lines(text, font, max_width):
        lines = []
        words = text.split(', ')
        if not words:
            return []
        
        current_line = words[0]
        for word in words[1:]:
            test_line = current_line + ', ' + word
            if draw.textlength(process_text(test_line), font) <= max_width:
                current_line = test_line
            else:
                lines.append(current_line)
                current_line = word
        lines.append(current_line)
        return lines

    wrapped_test_lines = get_wrapped_lines(test_names_str, font_tests, content_width) # Use font_tests

    # تحسين عرض التحاليل مع تباعد أفضل ووضوح أكبر
    for i, line in enumerate(wrapped_test_lines):
        if not line.strip():
            continue
        if current_y < label_height_px - padding_px - 8:  # ترك مساحة كافية في الأسفل
             processed_line = process_text(line)
             draw.text((padding_px, current_y), processed_line, font=font_tests, fill=text_color, anchor="la")
             bbox = draw.textbbox((0,0), processed_line, font=font_tests)
             line_height = bbox[3] - bbox[1]
             # تباعد محسن بين الأسطر
             current_y += line_height + (line_padding_px if i < len(wrapped_test_lines) - 1 else 0)
        else:
             break

    return image


def get_printer_list():
    """
    Retrieves a list of available printers on the system.
    """
    import win32print
    import win32ui
    import win32con

    printers = []
    try:
        h_printers = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL, None, 2)
        for i in range(len(h_printers)):
            printers.append(h_printers[i][2])
    except Exception as e:
        print(f"Error enumerating printers: {e}")
    return printers
