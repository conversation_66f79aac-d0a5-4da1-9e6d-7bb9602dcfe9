#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار معاينة الستكر - لحفظ صورة الستكر كملف لمعاينة التحسينات
"""

import json
from label_design import create_compact_label_image

def test_label_preview():
    """إنشاء معاينة للستكر وحفظها كصورة"""
    
    print("🖼️  اختبار معاينة الستكر")
    print("=" * 40)
    
    # تحميل الإعدادات
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("❌ ملف الإعدادات غير موجود")
        return
    
    # بيانات تجريبية محسنة
    test_data = {
        "labOrder": {
            "patient": {
                "name": "أحمد محمد علي الخالدي"
            },
            "created_at_raw": "2024-01-15T10:30:00Z"
        },
        "sampleGroups": {
            "group1": {
                "sample_code": "LAB-2024-001234",
                "name": "Serum",
                "tests": [
                    {"name": "CBC"},
                    {"name": "Glucose"},
                    {"name": "Urea"},
                    {"name": "Creatinine"},
                    {"name": "ALT"},
                    {"name": "AST"},
                    {"name": "Cholesterol"},
                    {"name": "Triglycerides"},
                    {"name": "HDL"},
                    {"name": "LDL"}
                ]
            }
        }
    }
    
    print("📋 البيانات التجريبية:")
    print(f"   المريض: {test_data['labOrder']['patient']['name']}")
    print(f"   رمز العينة: {test_data['sampleGroups']['group1']['sample_code']}")
    print(f"   نوع العينة: {test_data['sampleGroups']['group1']['name']}")
    print(f"   عدد التحاليل: {len(test_data['sampleGroups']['group1']['tests'])}")
    print()
    
    # إنشاء صورة الستكر
    print("🎨 إنشاء صورة الستكر...")
    try:
        lab_order_info = test_data['labOrder']
        sample_group = test_data['sampleGroups']['group1']
        
        label_image = create_compact_label_image(
            lab_order_info=lab_order_info,
            sample_group=sample_group,
            config=config
        )
        
        # حفظ الصورة
        output_file = "label_preview.png"
        label_image.save(output_file, "PNG")
        
        print(f"✅ تم حفظ معاينة الستكر في: {output_file}")
        print(f"📏 أبعاد الصورة: {label_image.width} × {label_image.height} بكسل")
        print(f"📐 الأبعاد الفعلية: {config.get('label_width_mm', 50)}mm × {config.get('label_height_mm', 25)}mm")
        
        # عرض التحسينات المطبقة
        print("\n🔧 التحسينات المطبقة:")
        print(f"   • تقليل المسافة العلوية: {config.get('padding_mm', 0.5)}mm")
        print(f"   • تكبير عرض الباركود: عامل {config.get('barcode_width_factor', 1.3)}")
        print(f"   • تحسين خط التحاليل: {config.get('font_size_pt_tests', 9)}pt")
        print(f"   • تقليل التباعد بين الأقسام: {config.get('section_gap_px', 6)}px")
        print(f"   • فاصل التحاليل المحسن: ◦ بدلاً من ,")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الستكر: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_label_preview()
