#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار دقة الطابعة - إنشاء صورة بدقة الطابعة الفعلية
"""

import json
import win32print
import win32ui
import win32con
from label_design import create_compact_label_image

def test_printer_dpi():
    """اختبار إنشاء صورة بدقة الطابعة الفعلية"""
    
    print("🖨️  اختبار دقة الطابعة")
    print("=" * 40)
    
    # تحميل الإعدادات
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("❌ ملف الإعدادات غير موجود")
        return
    
    printer_name = config.get("printer_name")
    if not printer_name:
        print("❌ اسم الطابعة غير محدد في الإعدادات")
        return
    
    print(f"📄 الطابعة: {printer_name}")
    
    # الحصول على دقة الطابعة الفعلية
    try:
        hprinter = win32print.OpenPrinter(printer_name)
        pdc = win32ui.CreateDC()
        pdc.CreatePrinterDC(printer_name)
        
        printer_dpi_x = pdc.GetDeviceCaps(win32con.LOGPIXELSX)
        printer_dpi_y = pdc.GetDeviceCaps(win32con.LOGPIXELSY)
        
        pdc.DeleteDC()
        win32print.ClosePrinter(hprinter)
        
        print(f"📏 دقة الطابعة الفعلية: {printer_dpi_x} × {printer_dpi_y} DPI")
        
        # استخدام أقل دقة للتوافق
        actual_dpi = min(printer_dpi_x, printer_dpi_y)
        print(f"📐 الدقة المستخدمة: {actual_dpi} DPI")
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على معلومات الطابعة: {e}")
        actual_dpi = 203  # دقة افتراضية للطابعات الحرارية
        print(f"📐 استخدام الدقة الافتراضية: {actual_dpi} DPI")
    
    # بيانات تجريبية
    test_data = {
        "labOrder": {
            "patient": {
                "name": "أحمد محمد الألفي"
            },
            "created_at_raw": "2024-01-15T10:30:00Z"
        },
        "sampleGroups": {
            "group1": {
                "sample_code": "LAB-2024-001234",
                "name": "Serum",
                "tests": [
                    {"name": "CBC"},
                    {"name": "Glucose"},
                    {"name": "الألف والباء"},
                    {"name": "Creatinine"},
                    {"name": "تحليل الدم"},
                    {"name": "AST"}
                ]
            }
        }
    }
    
    # إنشاء إعدادات بدقة الطابعة
    config_with_printer_dpi = config.copy()
    config_with_printer_dpi["dpi"] = actual_dpi
    
    print("\n🎨 إنشاء صورة بدقة الطابعة...")
    try:
        lab_order_info = test_data['labOrder']
        sample_group = test_data['sampleGroups']['group1']
        
        # إنشاء صورة بدقة الطابعة الفعلية
        label_image = create_compact_label_image(
            lab_order_info=lab_order_info,
            sample_group=sample_group,
            config=config_with_printer_dpi
        )
        
        # حفظ الصورة
        output_file = f"printer_dpi_{actual_dpi}_preview.png"
        label_image.save(output_file, "PNG")
        
        print(f"✅ تم حفظ الصورة بدقة الطابعة في: {output_file}")
        print(f"📏 أبعاد الصورة: {label_image.width} × {label_image.height} بكسل")
        
        # مقارنة مع الدقة الأصلية
        original_dpi = 300
        original_width = int((config.get('label_width_mm', 50) / 25.4) * original_dpi)
        original_height = int((config.get('label_height_mm', 25) / 25.4) * original_dpi)
        
        print(f"\n📊 مقارنة الدقة:")
        print(f"   • الدقة الأصلية: {original_dpi} DPI ({original_width}×{original_height}px)")
        print(f"   • دقة الطابعة: {actual_dpi} DPI ({label_image.width}×{label_image.height}px)")
        
        scale_factor = actual_dpi / original_dpi
        print(f"   • عامل التحجيم: {scale_factor:.3f}")
        
        if scale_factor < 1:
            print("   ⚠️  دقة الطابعة أقل - قد يؤثر على جودة النصوص")
        else:
            print("   ✅ دقة الطابعة مناسبة أو أعلى")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الصورة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_printer_dpi()
