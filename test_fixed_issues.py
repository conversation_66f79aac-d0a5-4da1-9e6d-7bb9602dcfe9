#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات - التحقق من حل مشاكل الحروف العربية والفواصل
"""

import json
from label_design import create_compact_label_image

def test_fixed_issues():
    """اختبار الإصلاحات المطبقة"""
    
    print("🔧 اختبار الإصلاحات")
    print("=" * 40)
    
    # تحميل الإعدادات
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("❌ ملف الإعدادات غير موجود")
        return
    
    # بيانات تجريبية تحتوي على حروف مشكلة
    test_data = {
        "labOrder": {
            "patient": {
                "name": "أحمد محمد الألفي"  # يحتوي على حرف الألف
            },
            "created_at_raw": "2024-01-15T10:30:00Z"
        },
        "sampleGroups": {
            "group1": {
                "sample_code": "LAB-2024-001234",
                "name": "Serum",
                "tests": [
                    {"name": "CBC"},
                    {"name": "Glucose"},
                    {"name": "الألف والباء"},  # نص عربي يحتوي على الألف
                    {"name": "Creatinine"},
                    {"name": "تحليل الدم"},
                    {"name": "AST"},
                    {"name": "الكوليسترول"}
                ]
            }
        }
    }
    
    print("📋 البيانات التجريبية:")
    print(f"   المريض: {test_data['labOrder']['patient']['name']}")
    print(f"   رمز العينة: {test_data['sampleGroups']['group1']['sample_code']}")
    print(f"   نوع العينة: {test_data['sampleGroups']['group1']['name']}")
    print()
    
    print("🔤 التحاليل (بدون فواصل):")
    tests = test_data['sampleGroups']['group1']['tests']
    for i, test in enumerate(tests, 1):
        print(f"   {i}. {test['name']}")
    print()
    
    print("🎨 إنشاء صورة الستكر...")
    try:
        lab_order_info = test_data['labOrder']
        sample_group = test_data['sampleGroups']['group1']
        
        label_image = create_compact_label_image(
            lab_order_info=lab_order_info,
            sample_group=sample_group,
            config=config
        )
        
        # حفظ الصورة
        output_file = "fixed_label_preview.png"
        label_image.save(output_file, "PNG")
        
        print(f"✅ تم حفظ الستكر المحسن في: {output_file}")
        
        # عرض الإصلاحات المطبقة
        print("\n🔧 الإصلاحات المطبقة:")
        print("   ✅ تم تغيير الخط إلى NotoNaskhArabic لدعم أفضل للعربية")
        print("   ✅ تم إزالة الفواصل بين التحاليل")
        print("   ✅ تم تحسين معالجة النصوص العربية")
        print("   ✅ تم إضافة فراغات بسيطة بين التحاليل")
        
        # معلومات الخط الجديد
        print(f"\n📝 معلومات الخط:")
        print(f"   • الخط الغامق: {config.get('font_bold_name')}")
        print(f"   • الخط العادي: {config.get('font_regular_name')}")
        
        # نص التحاليل النهائي
        test_names_str = '   '.join([test.get('name', '') for test in tests])
        print(f"\n📋 نص التحاليل النهائي:")
        print(f"   {test_names_str}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الستكر: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_issues()
